import React, { useState, useEffect, Suspense, lazy } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControlLabel,
  Checkbox,
  Alert,
  Divider,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Chip,
  Tabs,
  Tab,
  IconButton,
  ToggleButton,
  ToggleButtonGroup,
  Collapse,
  Tooltip as MuiTooltip,
  Fade,
  Zoom,
  Switch,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Stack,
  LinearProgress,
  Badge,
  Skeleton,
  ButtonGroup,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  AccordionSummary,
  AccordionDetails,
  Accordion,
  TextField,
  Radio,
  RadioGroup,
  FormLabel
} from '@mui/material';

// Lazy load the PlotlyBoxPlot component
const PlotlyBoxPlot = lazy(() => import('./PlotlyBoxPlot'));

import {
  Assessment as AssessmentIcon,
  Functions as FunctionsIcon,
  Insert<PERSON>hart as InsertChartIcon,
  ShowChart as ShowChartIcon,
  BarChart as BarChartIcon,
  BubbleChart as BubbleChartIcon,
  DonutLarge as DonutLargeIcon,
  Timeline as TimelineIcon,
  FileDownload as FileDownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  CompareArrows as CompareArrowsIcon,
  FilterList as FilterListIcon,
  Settings as SettingsIcon,
  HelpOutline as HelpOutlineIcon,
  ExpandMore as ExpandMoreIcon,
  Speed as SpeedIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as RemoveIcon,
  Insights as InsightsIcon,
  Calculate as CalculateIcon,
  Refresh as RefreshIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  GroupWork as GroupWorkIcon,
  FilterAlt as FilterAltIcon,
  CenterFocusStrong as CenterFocusStrongIcon,
  ScatterPlot as ScatterPlotIcon,
  TrendingFlat as TrendingFlatIcon,
  Straighten as StraightenIcon,
  Info as InfoIcon,
  People as PeopleIcon,
  DataUsage as DataUsageIcon
} from '@mui/icons-material';

import { useData } from '../../context/DataContext';
import { DataType, ColumnStatistics } from '../../types';
import StatsCard from '../UI/StatsCard';
import {
  calculateMean,
  calculateMedian,
  calculateMode,
  ModeResult,
  calculateVariance,
  calculateStandardDeviation,
  calculateRange,
  calculateQuartiles,
  calculateIQR,
  calculateSkewness,
  calculateKurtosis,
  comprehensiveNormalityTest
} from '@/utils/stats';
import {
  extractNumericValues,
  safeSortNumeric,
  safeCalculate,
  filterOutliers as filterOutliersUtil,
  extractNumericValuesEnhanced
} from '@/utils/typeConversions';
import { getValidSampleSize, getColumnMissingSummary } from '@/utils/missingDataUtils';

import {
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  BarChart,
  ScatterChart,
  Scatter,
  ReferenceLine,
  Label,
  AreaChart,
  Area,
  ComposedChart,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  PieChart,
  Pie
} from 'recharts';

// Additional statistical calculations
const calculateCoefficientOfVariation = (values: number[]): number => {
  const mean = calculateMean(values);
  const std = calculateStandardDeviation(values);
  return mean !== 0 ? (std / Math.abs(mean)) * 100 : 0;
};

const calculateStandardError = (values: number[]): number => {
  const std = calculateStandardDeviation(values);
  return std / Math.sqrt(values.length);
};

const calculateConfidenceInterval = (values: number[], confidence: number = 0.95): [number, number] => {
  const mean = calculateMean(values);
  const se = calculateStandardError(values);
  const z = confidence === 0.95 ? 1.96 : confidence === 0.99 ? 2.576 : 1.645;
  return [mean - z * se, mean + z * se];
};

const calculatePercentileRank = (values: number[], value: number): number => {
  const sorted = [...values].sort((a, b) => a - b);
  const index = sorted.findIndex(v => v >= value);
  return index === -1 ? 100 : (index / sorted.length) * 100;
};

// Advanced statistics calculations
const calculateGeometricMean = (values: number[]): number => {
  const positiveValues = values.filter(v => v > 0);
  if (positiveValues.length === 0) return 0;
  const product = positiveValues.reduce((acc, val) => acc * val, 1);
  return Math.pow(product, 1 / positiveValues.length);
};

const calculateHarmonicMean = (values: number[]): number => {
  const positiveValues = values.filter(v => v > 0);
  if (positiveValues.length === 0) return 0;
  const reciprocalSum = positiveValues.reduce((acc, val) => acc + 1 / val, 0);
  return positiveValues.length / reciprocalSum;
};

const calculateTrimmedMean = (values: number[], trimPercent: number = 0.05): number => {
  const sorted = [...values].sort((a, b) => a - b);
  const trimCount = Math.floor(sorted.length * trimPercent);
  const trimmed = sorted.slice(trimCount, sorted.length - trimCount);
  return calculateMean(trimmed);
};

const calculateMAD = (values: number[]): number => {
  const median = calculateMedian(values);
  const deviations = values.map(v => Math.abs(v - median));
  return calculateMedian(deviations);
};

// Custom styled components
const StatCard = ({ title, value, subtitle, icon, color = 'primary', trend }: any) => {
  const theme = useTheme();

  // Define muted color palette for better visual balance
  const getCardColors = (colorName: string) => {
    const colorMap = {
      primary: {
        background: theme.palette.mode === 'dark' ? 'rgba(33, 150, 243, 0.08)' : 'rgba(25, 118, 210, 0.04)',
        iconBackground: theme.palette.mode === 'dark' ? 'rgba(33, 150, 243, 0.12)' : 'rgba(25, 118, 210, 0.08)',
        iconColor: theme.palette.mode === 'dark' ? 'rgba(33, 150, 243, 0.7)' : 'rgba(25, 118, 210, 0.7)',
        border: theme.palette.mode === 'dark' ? 'rgba(33, 150, 243, 0.12)' : 'rgba(25, 118, 210, 0.12)'
      },
      secondary: {
        background: theme.palette.mode === 'dark' ? 'rgba(156, 39, 176, 0.08)' : 'rgba(156, 39, 176, 0.04)',
        iconBackground: theme.palette.mode === 'dark' ? 'rgba(156, 39, 176, 0.12)' : 'rgba(156, 39, 176, 0.08)',
        iconColor: theme.palette.mode === 'dark' ? 'rgba(156, 39, 176, 0.7)' : 'rgba(156, 39, 176, 0.7)',
        border: theme.palette.mode === 'dark' ? 'rgba(156, 39, 176, 0.12)' : 'rgba(156, 39, 176, 0.12)'
      },
      success: {
        background: theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.08)' : 'rgba(46, 125, 50, 0.04)',
        iconBackground: theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.12)' : 'rgba(46, 125, 50, 0.08)',
        iconColor: theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.7)' : 'rgba(46, 125, 50, 0.7)',
        border: theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.12)' : 'rgba(46, 125, 50, 0.12)'
      },
      warning: {
        background: theme.palette.mode === 'dark' ? 'rgba(255, 152, 0, 0.08)' : 'rgba(237, 108, 2, 0.04)',
        iconBackground: theme.palette.mode === 'dark' ? 'rgba(255, 152, 0, 0.12)' : 'rgba(237, 108, 2, 0.08)',
        iconColor: theme.palette.mode === 'dark' ? 'rgba(255, 152, 0, 0.7)' : 'rgba(237, 108, 2, 0.7)',
        border: theme.palette.mode === 'dark' ? 'rgba(255, 152, 0, 0.12)' : 'rgba(237, 108, 2, 0.12)'
      }
    };
    return colorMap[colorName as keyof typeof colorMap] || colorMap.primary;
  };

  const cardColors = getCardColors(color);

  return (
    <Card
      sx={{
        height: '100%',
        background: cardColors.background,
        border: `1px solid ${cardColors.border}`,
        borderRadius: 2,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&:hover': {
          boxShadow: theme.palette.mode === 'dark'
            ? '0 8px 32px rgba(0, 0, 0, 0.3)'
            : '0 8px 32px rgba(0, 0, 0, 0.08)',
          transform: 'translateY(-2px)',
          borderColor: cardColors.iconColor
        }
      }}
    >
      <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
        <Box display="flex" alignItems="flex-start" justifyContent="space-between" mb={2}>
          <Box flex={1} mr={2}>
            <Typography
              color="text.secondary"
              variant="body2"
              fontWeight={500}
              sx={{
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                fontSize: '0.75rem',
                mb: 1
              }}
            >
              {title}
            </Typography>
            <Typography
              variant="h4"
              component="div"
              fontWeight={600}
              sx={{
                color: theme.palette.text.primary,
                lineHeight: 1.2,
                mb: 0.5
              }}
            >
              {value}
            </Typography>
          </Box>
          <Box
            sx={{
              width: 56,
              height: 56,
              borderRadius: 2,
              background: cardColors.iconBackground,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0
            }}
          >
            <Box
              sx={{
                color: cardColors.iconColor,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                '& svg': {
                  fontSize: '1.5rem'
                }
              }}
            >
              {icon}
            </Box>
          </Box>
        </Box>

        {subtitle && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              fontSize: '0.875rem',
              lineHeight: 1.4,
              fontWeight: 400
            }}
          >
            {subtitle}
          </Typography>
        )}

        {trend && (
          <Box display="flex" alignItems="center" mt={2} pt={2} borderTop={`1px solid ${cardColors.border}`}>
            {trend > 0 ? (
              <TrendingUpIcon sx={{ color: 'success.main', fontSize: '1rem', mr: 0.5 }} />
            ) : trend < 0 ? (
              <TrendingDownIcon sx={{ color: 'error.main', fontSize: '1rem', mr: 0.5 }} />
            ) : (
              <RemoveIcon sx={{ color: 'text.disabled', fontSize: '1rem', mr: 0.5 }} />
            )}
            <Typography
              variant="body2"
              fontWeight={500}
              color={trend > 0 ? 'success.main' : trend < 0 ? 'error.main' : 'text.secondary'}
            >
              {Math.abs(trend).toFixed(1)}%
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Component for descriptive analysis of quantitative data
const DescriptiveAnalysis: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  
  // State for analysis options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');

  // Sync selectedDatasetId with currentDataset changes (including filtered datasets)
  useEffect(() => {
    if (currentDataset?.id && currentDataset.id !== selectedDatasetId) {
      setSelectedDatasetId(currentDataset.id);
    }
  }, [currentDataset?.id, selectedDatasetId]);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [includeShape, setIncludeShape] = useState<boolean>(true);
  const [includeOutliers, setIncludeOutliers] = useState<boolean>(true);
  const [includeNormalityTest, setIncludeNormalityTest] = useState<boolean>(true);
  const [includeBoxPlot, setIncludeBoxPlot] = useState<boolean>(true);
  const [includeConfidenceIntervals, setIncludeConfidenceIntervals] = useState<boolean>(true);
  const [confidenceLevel, setConfidenceLevel] = useState<number>(0.95);
  const [comparisonMode, setComparisonMode] = useState<boolean>(false);
  const [groupingVariable, setGroupingVariable] = useState<string>('');
  const [visualizationType, setVisualizationType] = useState<string>('histogram');
  const [showAdvancedStats, setShowAdvancedStats] = useState<boolean>(false);
  const [filterOutliers, setFilterOutliers] = useState<boolean>(false);
  const [outlierMethod, setOutlierMethod] = useState<'iqr' | 'zscore'>('iqr');
  const [outlierThreshold, setOutlierThreshold] = useState<number>(1.5);
  
  // UI state
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  
  // Enhanced state for analysis results
  const [descriptiveResults, setDescriptiveResults] = useState<{
    [columnId: string]: {
      column: any;
      statistics: {
        n: number;
        missing: number;
        mean: number;
        median: number;
        modeResult: ModeResult;
        variance: number;
        standardDeviation: number;
        min: number;
        max: number;
        range: number;
        quartiles: [number, number, number];
        iqr: number;
        coefficientOfVariation?: number;
        standardError?: number;
        confidenceInterval?: [number, number];
        percentiles?: { [key: string]: number };
        skewness?: number;
        kurtosis?: number;
        isNormal?: {
          isNormal: boolean;
          pValue: number;
          statistic: number;
        };
        // Advanced statistics
        geometricMean?: number;
        harmonicMean?: number;
        trimmedMean?: number;
        mad?: number;
        outliersRemoved?: number;
        originalN?: number;
      };
      // Group statistics (when comparison mode is enabled)
      groupStatistics?: {
        [groupValue: string]: {
          n: number;
          mean: number;
          median: number;
          std: number;
          min: number;
          max: number;
          quartiles: [number, number, number];
          values?: number[];
        };
      };
      histogramData: Array<{
        bin: string;
        binMiddle: number;
        binStart: number;
        binEnd: number;
        frequency: number;
        relativeFrequency: number;
        cumulativeFrequency: number;
      }>;
      densityData: Array<{
        x: number;
        y: number;
      }>;
      qqPlotData: Array<{
        theoretical: number;
        sample: number;
      }>;
      boxPlotData: {
        min: number;
        q1: number;
        median: number;
        q3: number;
        max: number;
        outliers: number[];
      };
    }
  }>({});
  
  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedResults = localStorage.getItem('descriptive_analysis_results');
    const savedConfig = localStorage.getItem('descriptive_analysis_config');
    
    if (savedResults) {
      try {
        setDescriptiveResults(JSON.parse(savedResults));
      } catch (e) {
        console.error('Error parsing saved descriptive analysis results:', e);
      }
    }
    
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setIncludeShape(config.includeShape ?? true);
        setIncludeOutliers(config.includeOutliers ?? true);
        setIncludeNormalityTest(config.includeNormalityTest ?? true);
        setIncludeBoxPlot(config.includeBoxPlot ?? true);
        setIncludeConfidenceIntervals(config.includeConfidenceIntervals ?? true);
        setConfidenceLevel(config.confidenceLevel ?? 0.95);
        setComparisonMode(config.comparisonMode ?? false);
        setGroupingVariable(config.groupingVariable ?? '');
        setShowAdvancedStats(config.showAdvancedStats ?? false);
        setFilterOutliers(config.filterOutliers ?? false);
        setOutlierMethod(config.outlierMethod ?? 'iqr');
        setOutlierThreshold(config.outlierThreshold ?? 1.5);
      } catch (e) {
        console.error('Error parsing saved configuration:', e);
      }
    }
  }, []);
  
  // Save configuration when it changes
  useEffect(() => {
    const config = {
      includeShape,
      includeOutliers,
      includeNormalityTest,
      includeBoxPlot,
      includeConfidenceIntervals,
      confidenceLevel,
      comparisonMode,
      groupingVariable,
      showAdvancedStats,
      filterOutliers,
      outlierMethod,
      outlierThreshold
    };
    localStorage.setItem('descriptive_analysis_config', JSON.stringify(config));
  }, [
    includeShape,
    includeOutliers,
    includeNormalityTest,
    includeBoxPlot,
    includeConfidenceIntervals,
    confidenceLevel,
    comparisonMode,
    groupingVariable,
    showAdvancedStats,
    filterOutliers,
    outlierMethod,
    outlierThreshold
  ]);
  
  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Get categorical columns for grouping
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];
  
  // Use all numeric columns
  const filteredColumns = numericColumns;
  
  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedColumns([]);
    setGroupingVariable('');
    setDescriptiveResults({});

    // Update the current dataset in context
    const selectedDataset = datasets.find(ds => ds.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }

    localStorage.removeItem('descriptive_analysis_results');
  };
  
  // Handle column selection change
  const handleColumnChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedColumns(typeof value === 'string' ? [value] : value);
    
    setDescriptiveResults({});
    localStorage.removeItem('descriptive_analysis_results');
  };
  
  // Filter outliers based on selected method
  const filterOutliersFromData = (values: number[], method: 'iqr' | 'zscore', threshold: number): { filtered: number[], removed: number } => {
    if (method === 'iqr') {
      const q1 = calculateQuartiles(values)[0];
      const q3 = calculateQuartiles(values)[2];
      const iqr = q3 - q1;
      const lowerBound = q1 - threshold * iqr;
      const upperBound = q3 + threshold * iqr;
      const filtered = values.filter(val => val >= lowerBound && val <= upperBound);
      return { filtered, removed: values.length - filtered.length };
    } else {
      // Z-score method
      const mean = calculateMean(values);
      const std = calculateStandardDeviation(values);
      const filtered = values.filter(val => Math.abs((val - mean) / std) <= threshold);
      return { filtered, removed: values.length - filtered.length };
    }
  };
  
  // Generate normal distribution data for Q-Q plot
  const generateQQPlotData = (values: number[]): Array<{ theoretical: number; sample: number }> => {
    const sorted = [...values].sort((a, b) => a - b);
    const n = sorted.length;
    const mean = calculateMean(values);
    const std = calculateStandardDeviation(values);
    
    if (n === 0 || std === 0) {
      return [];
    }

    return sorted.map((value, i) => {
      const p = (i + 0.5) / n;
      const z = Math.sqrt(2) * erfInv(2 * p - 1);
      return {
        theoretical: mean + z * std,
        sample: value
      };
    });
  };
  
  // Error function inverse approximation
  const erfInv = (x: number): number => {
    const a = 0.147;
    const sign = x < 0 ? -1 : 1;
    x = Math.abs(x);
    
    const ln1mx2 = Math.log(1 - x * x);
    const part1 = 2 / (Math.PI * a) + ln1mx2 / 2;
    const part2 = ln1mx2 / a;
    
    return sign * Math.sqrt(Math.sqrt(part1 * part1 - part2) - part1);
  };
  
  // Generate density curve data
  const generateDensityData = (values: number[]): Array<{ x: number; y: number }> => {
    if (values.length === 0) {
      return [];
    }
    
    const mean = calculateMean(values);
    const std = calculateStandardDeviation(values);
    
    if (std === 0) {
      return [{ x: mean, y: 1 }];
    }

    const min = Math.min(...values) - std;
    const max = Math.max(...values) + std;
    const points = 100;
    const step = (max - min) / points;
    
    const density = [];
    for (let i = 0; i <= points; i++) {
      const x = min + i * step;
      const y = (1 / (std * Math.sqrt(2 * Math.PI))) * 
                Math.exp(-0.5 * Math.pow((x - mean) / std, 2));
      density.push({ x, y });
    }
    
    return density;
  };
  
  // Run descriptive analysis
  const runDescriptiveAnalysis = () => {
    if (!currentDataset || selectedColumns.length === 0) {
      setError('Please select a dataset and at least one numeric column to analyze.');
      return;
    }
    
    if (comparisonMode && !groupingVariable) {
      setError('Please select a grouping variable for comparison mode.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    setTimeout(() => {
      try {
        const selectedColumnsData = currentDataset.columns.filter(
          col => selectedColumns.includes(col.id)
        );
        
        const results: any = {};
        
        selectedColumnsData.forEach(column => {
          // Get all values for this column (including missing)
          const allValues = currentDataset.data.map(row => row[column.name]);

          // Use enhanced extraction that respects user-defined missing value codes
          let numericValues = extractNumericValuesEnhanced(currentDataset.data, column);

          if (numericValues.length === 0) {
            return;
          }

          // Get missing value summary
          const missingSummary = getColumnMissingSummary(currentDataset.data, column);

          // Store original count (before outlier filtering)
          const originalN = numericValues.length;
          let outliersRemoved = 0;

          // Apply outlier filtering if requested
          if (filterOutliers) {
            if (outlierMethod === 'iqr') {
              const filterResult = filterOutliersUtil(allValues, outlierThreshold);
              numericValues = filterResult.filtered;
              outliersRemoved = originalN - filterResult.filtered.length;
            } else {
              // Z-score method fallback
              const filterResult = filterOutliersFromData(numericValues, outlierMethod, outlierThreshold);
              numericValues = filterResult.filtered;
              outliersRemoved = filterResult.removed;
            }
          }

          // Calculate basic statistics
          const n = numericValues.length;
          const missing = missingSummary.missingCount;
          const mean = calculateMean(numericValues);
          const median = calculateMedian(numericValues);
          const modeResult = calculateMode(numericValues);
          const variance = calculateVariance(numericValues);
          const standardDeviation = calculateStandardDeviation(numericValues);
          const min = numericValues.length > 0 ? Math.min(...numericValues) : 0;
          const max = numericValues.length > 0 ? Math.max(...numericValues) : 0;
          const range = calculateRange(numericValues);
          const quartiles = calculateQuartiles(numericValues);
          const iqr = calculateIQR(numericValues);
          
          // Additional statistics
          const coefficientOfVariation = calculateCoefficientOfVariation(numericValues);
          const standardError = calculateStandardError(numericValues);
          const confidenceInterval = includeConfidenceIntervals 
            ? calculateConfidenceInterval(numericValues, confidenceLevel)
            : undefined;
          
          // Calculate percentiles
          const percentiles: { [key: string]: number } = {};
          if (numericValues.length > 0) {
            [5, 10, 20, 30, 40, 60, 70, 80, 90, 95].forEach(p => {
              const index = Math.ceil((p / 100) * n) - 1;
              percentiles[`p${p}`] = numericValues[Math.max(0, Math.min(index, n - 1))];
            });
          }
          
          // Advanced statistics
          let geometricMean, harmonicMean, trimmedMean, mad;
          if (showAdvancedStats) {
            geometricMean = calculateGeometricMean(numericValues);
            harmonicMean = calculateHarmonicMean(numericValues);
            trimmedMean = calculateTrimmedMean(numericValues);
            mad = calculateMAD(numericValues);
          }
          
          // Shape statistics
          let skewness, kurtosis, isNormal;
          
          if (includeShape) {
            skewness = calculateSkewness(numericValues);
            kurtosis = calculateKurtosis(numericValues);
          }
          
          if (includeNormalityTest) {
            const normalityTest = comprehensiveNormalityTest(numericValues, 0.05, ['auto']);
            isNormal = {
              isNormal: normalityTest.overallAssessment.isNormal,
              pValue: normalityTest.tests.shapiroWilk?.pValue ||
                      normalityTest.tests.kolmogorovSmirnov?.pValue ||
                      normalityTest.tests.jarqueBera?.pValue || NaN,
              statistic: normalityTest.tests.shapiroWilk?.statistic ||
                        normalityTest.tests.kolmogorovSmirnov?.statistic ||
                        normalityTest.tests.jarqueBera?.statistic || NaN
            };
          }
          
          // Group analysis for comparison mode
          let groupStatistics: any = undefined;
          if (comparisonMode && groupingVariable) {
            const groupCol = currentDataset.columns.find(col => col.id === groupingVariable);
            if (groupCol) {
              groupStatistics = {};
              const groups: { [key: string]: number[] } = {};
              
              // Group the data using enhanced missing data handling
              currentDataset.data.forEach((row) => {
                const value = row[column.name];
                const groupValue = row[groupCol.name];

                // Check if the main value is missing
                const valueInfo = isMissingValue(value, column);
                // Check if the grouping value is missing
                const groupInfo = isMissingValue(groupValue, groupCol);

                const group = groupInfo.isMissing ? 'Missing' : String(groupValue);

                // Only include non-missing numeric values
                if (!valueInfo.isMissing && typeof value === 'number' && !isNaN(value)) {
                  if (!groups[group]) groups[group] = [];
                  groups[group].push(value);
                }
              });
              
              // Calculate statistics for each group
              Object.entries(groups).forEach(([groupName, groupValues]) => {
                let filteredGroupValues = [...groupValues].sort((a, b) => a - b);
                
                // Apply outlier filtering to groups if enabled
                if (filterOutliers) {
                  const filterResult = filterOutliersFromData(filteredGroupValues, outlierMethod, outlierThreshold);
                  filteredGroupValues = filterResult.filtered;
                }
                
                groupStatistics[groupName] = {
                  n: filteredGroupValues.length,
                  mean: calculateMean(filteredGroupValues),
                  median: calculateMedian(filteredGroupValues),
                  std: calculateStandardDeviation(filteredGroupValues),
                  min: Math.min(...filteredGroupValues),
                  max: Math.max(...filteredGroupValues),
                  quartiles: calculateQuartiles(filteredGroupValues),
                  values: filteredGroupValues
                };
              });
            }
          }
          
          // Generate histogram data with cumulative frequency
          const numBins = Math.max(5, Math.ceil(1 + 3.322 * Math.log10(n)));
          const binWidth = range / numBins;
          
          const histogramData = [];
          let cumulativeCount = 0;
          
          for (let i = 0; i < numBins; i++) {
            const binStart = min + i * binWidth;
            const binEnd = min + (i + 1) * binWidth;
            const binCount = numericValues.filter(
              val => val >= binStart && (i === numBins - 1 ? val <= binEnd : val < binEnd)
            ).length;
            
            cumulativeCount += binCount;
            const binMiddle = (binStart + binEnd) / 2;
            
            histogramData.push({
              bin: `${binStart.toFixed(2)}-${binEnd.toFixed(2)}`,
              binMiddle: binMiddle,
              binStart,
              binEnd,
              frequency: binCount,
              relativeFrequency: binCount / n,
              cumulativeFrequency: cumulativeCount / n
            });
          }
          
          // Generate density and Q-Q plot data
          const densityData = generateDensityData(numericValues);
          const qqPlotData = generateQQPlotData(numericValues);
          
          // Identify outliers (using original numeric data for outlier detection)
          const allNumericValues = extractNumericValuesEnhanced(currentDataset.data, column);
          
          const q1All = calculateQuartiles(allNumericValues)[0];
          const q3All = calculateQuartiles(allNumericValues)[2];
          const iqrAll = q3All - q1All;
          const lowerBound = q1All - 1.5 * iqrAll;
          const upperBound = q3All + 1.5 * iqrAll;
          const outliers = allNumericValues.filter(
            val => val < lowerBound || val > upperBound
          );
          
          const boxPlotData = {
            min: includeOutliers ? allNumericValues.find(val => val >= lowerBound) || min : min,
            q1: quartiles[0],
            median: quartiles[1],
            q3: quartiles[2],
            max: includeOutliers ? [...allNumericValues].reverse().find(val => val <= upperBound) || max : max,
            outliers: includeOutliers ? outliers : []
          };
          
          results[column.id] = {
            column,
            statistics: {
              n,
              missing,
              mean,
              median,
              modeResult,
              variance,
              standardDeviation,
              min,
              max,
              range,
              quartiles,
              iqr,
              coefficientOfVariation,
              standardError,
              confidenceInterval,
              percentiles,
              ...(includeShape ? { skewness, kurtosis } : {}),
              ...(includeNormalityTest ? { isNormal } : {}),
              ...(showAdvancedStats ? { geometricMean, harmonicMean, trimmedMean, mad } : {}),
              ...(filterOutliers ? { outliersRemoved, originalN } : {})
            },
            groupStatistics,
            histogramData,
            densityData,
            qqPlotData,
            boxPlotData
          };
        });
        
        setDescriptiveResults(results);
        localStorage.setItem('descriptive_analysis_results', JSON.stringify(results));
        
        setLoading(false);
      } catch (err) {
        setError(`Error analyzing data: ${err instanceof Error ? err.message : String(err)}`);
        setLoading(false);
      }
    }, 1000);
  };
  
  // Export results to CSV
  const exportToCSV = () => {
    if (Object.keys(descriptiveResults).length === 0) return;
    
    let csv = 'Variable,Statistic,Value\n';
    
    Object.entries(descriptiveResults).forEach(([, result]) => {
      const stats = result.statistics;
      csv += `${result.column.name},Sample Size,${stats.n}\n`;
      
      if (stats.outliersRemoved !== undefined) {
        csv += `${result.column.name},Original Sample Size,${stats.originalN}\n`;
        csv += `${result.column.name},Outliers Removed,${stats.outliersRemoved}\n`;
      }
      
      csv += `${result.column.name},Missing Values,${stats.missing}\n`;
      csv += `${result.column.name},Mean,${stats.mean.toFixed(4)}\n`;
      csv += `${result.column.name},Median,${stats.median.toFixed(4)}\n`;
      csv += `${result.column.name},Mode,${stats.modeResult.mode?.toFixed(4) || 'N/A'}\n`;
      csv += `${result.column.name},Standard Deviation,${stats.standardDeviation.toFixed(4)}\n`;
      csv += `${result.column.name},Variance,${stats.variance.toFixed(4)}\n`;
      csv += `${result.column.name},Min,${stats.min.toFixed(4)}\n`;
      csv += `${result.column.name},Max,${stats.max.toFixed(4)}\n`;
      csv += `${result.column.name},Range,${stats.range.toFixed(4)}\n`;
      csv += `${result.column.name},Q1,${stats.quartiles[0].toFixed(4)}\n`;
      csv += `${result.column.name},Q3,${stats.quartiles[2].toFixed(4)}\n`;
      csv += `${result.column.name},IQR,${stats.iqr.toFixed(4)}\n`;
      
      if (stats.coefficientOfVariation !== undefined) {
        csv += `${result.column.name},CV %,${stats.coefficientOfVariation.toFixed(2)}\n`;
      }
      
      if (stats.geometricMean !== undefined) {
        csv += `${result.column.name},Geometric Mean,${stats.geometricMean.toFixed(4)}\n`;
      }
      
      if (stats.harmonicMean !== undefined) {
        csv += `${result.column.name},Harmonic Mean,${stats.harmonicMean.toFixed(4)}\n`;
      }
      
      if (stats.trimmedMean !== undefined) {
        csv += `${result.column.name},Trimmed Mean (5%),${stats.trimmedMean.toFixed(4)}\n`;
      }
      
      if (stats.mad !== undefined) {
        csv += `${result.column.name},MAD,${stats.mad.toFixed(4)}\n`;
      }
      
      if (stats.skewness !== undefined) {
        csv += `${result.column.name},Skewness,${stats.skewness.toFixed(4)}\n`;
      }
      
      if (stats.kurtosis !== undefined) {
        csv += `${result.column.name},Kurtosis,${stats.kurtosis.toFixed(4)}\n`;
      }
      
      if (stats.isNormal) {
        csv += `${result.column.name},Normality p-value,${stats.isNormal.pValue.toFixed(4)}\n`;
      }
      
      // Add group statistics if available
      if (result.groupStatistics) {
        csv += '\n';
        csv += `${result.column.name} - Group Statistics\n`;
        csv += 'Group,N,Mean,Median,Std Dev,Min,Max\n';
        
        Object.entries(result.groupStatistics).forEach(([group, groupStats]: [string, any]) => {
          csv += `${group},${groupStats.n},${groupStats.mean.toFixed(4)},`;
          csv += `${groupStats.median.toFixed(4)},${groupStats.std.toFixed(4)},`;
          csv += `${groupStats.min.toFixed(4)},${groupStats.max.toFixed(4)}\n`;
        });
      }
      
      csv += '\n';
    });
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'descriptive_statistics.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };
  
  // Generate comparison data for radar chart
  const generateComparisonData = () => {
    if (!comparisonMode || selectedColumns.length < 2) return [];
    
    const metrics = ['Mean', 'Std Dev', 'CV%', 'Skewness', 'Kurtosis'];
    const data = metrics.map(metric => {
      const point: any = { metric };
      
      selectedColumns.forEach(columnId => {
        const result = descriptiveResults[columnId];
        if (result) {
          switch (metric) {
            case 'Mean':
              point[result.column.name] = result.statistics.mean;
              break;
            case 'Std Dev':
              point[result.column.name] = result.statistics.standardDeviation;
              break;
            case 'CV%':
              point[result.column.name] = result.statistics.coefficientOfVariation || 0;
              break;
            case 'Skewness':
              point[result.column.name] = Math.abs(result.statistics.skewness || 0);
              break;
            case 'Kurtosis':
              point[result.column.name] = result.statistics.kurtosis || 0;
              break;
          }
        }
      });
      
      return point;
    });
    
    return data;
  };
  
  // Generate group comparison chart data
  const generateGroupComparisonData = (columnId: string) => {
    const result = descriptiveResults[columnId];
    if (!result || !result.groupStatistics) return [];
    
    return Object.entries(result.groupStatistics).map(([group, stats]: [string, any]) => ({
      group,
      mean: stats.mean,
      median: stats.median,
      std: stats.std,
      n: stats.n
    }));
  };
  
  return (
    <Box p={3}>
      <Box display="flex" alignItems="center" mb={3}>
        <InsightsIcon sx={{ fontSize: 32, mr: 2, color: theme.palette.primary.main }} />
        <Typography variant="h4" component="h1">
          Descriptive Statistics Analysis
        </Typography>
        <Box ml="auto">
          <ButtonGroup variant="outlined" size="small">
            <MuiTooltip title="Grid View">
              <Button
                onClick={() => setViewMode('grid')}
                color={viewMode === 'grid' ? 'primary' : 'inherit'}
              >
                <ViewModuleIcon />
              </Button>
            </MuiTooltip>
            <MuiTooltip title="List View">
              <Button
                onClick={() => setViewMode('list')}
                color={viewMode === 'list' ? 'primary' : 'inherit'}
              >
                <ViewListIcon />
              </Button>
            </MuiTooltip>
          </ButtonGroup>
          <MuiTooltip title="Help">
            <IconButton
              sx={{ ml: 1 }}
              onClick={() => window.location.href = '/app#video-tutorials'}
            >
              <HelpOutlineIcon />
            </IconButton>
          </MuiTooltip>
        </Box>
      </Box>
      
      <Fade in timeout={500}>
        <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <FilterListIcon sx={{ mr: 1 }} />
            Data Selection
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal" variant="outlined">
                <InputLabel id="dataset-select-label">Dataset</InputLabel>
                <Select
                  labelId="dataset-select-label"
                  id="dataset-select"
                  value={selectedDatasetId}
                  label="Dataset"
                  onChange={handleDatasetChange}
                  disabled={datasets.length === 0}
                >
                  {datasets.length === 0 ? (
                    <MenuItem value="" disabled>
                      No datasets available
                    </MenuItem>
                  ) : (
                    datasets.map(dataset => (
                      <MenuItem key={dataset.id} value={dataset.id}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                          <span>{dataset.name}</span>
                          <Chip label={`${dataset.data.length} rows`} size="small" />
                        </Box>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal" variant="outlined">
                <InputLabel id="column-select-label">Numeric Variables</InputLabel>
                <Select
                  labelId="column-select-label"
                  id="column-select"
                  multiple
                  value={selectedColumns}
                  label="Numeric Variables"
                  onChange={handleColumnChange}
                  disabled={numericColumns.length === 0}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip 
                          key={value} 
                          label={numericColumns.find(col => col.id === value)?.name || value}
                          size="small"
                        />
                      ))}
                    </Box>
                  )}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    filteredColumns.map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        <Checkbox checked={selectedColumns.indexOf(column.id) > -1} />
                        <ListItemText primary={column.name} />
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          
          <Accordion 
            expanded={expandedAccordion === 'options'} 
            onChange={(_, isExpanded) => setExpandedAccordion(isExpanded ? 'options' : false)}
            sx={{ mt: 3 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography sx={{ display: 'flex', alignItems: 'center' }}>
                <SettingsIcon sx={{ mr: 1 }} />
                Analysis Options
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeShape}
                        onChange={(e) => setIncludeShape(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Distribution Shape"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeOutliers}
                        onChange={(e) => setIncludeOutliers(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Outlier Detection"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeNormalityTest}
                        onChange={(e) => setIncludeNormalityTest(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Normality Test"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeBoxPlot}
                        onChange={(e) => setIncludeBoxPlot(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Box Plot"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeConfidenceIntervals}
                        onChange={(e) => setIncludeConfidenceIntervals(e.target.checked)}
                        color="primary"
                      />
                    }
                    label="Confidence Intervals"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={comparisonMode}
                        onChange={(e) => {
                          setComparisonMode(e.target.checked);
                          if (!e.target.checked) {
                            setGroupingVariable('');
                          }
                        }}
                        color="primary"
                      />
                    }
                    label={
                      <Box display="flex" alignItems="center">
                        <GroupWorkIcon sx={{ mr: 0.5, fontSize: 20 }} />
                        Comparison Mode
                      </Box>
                    }
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showAdvancedStats}
                        onChange={(e) => setShowAdvancedStats(e.target.checked)}
                        color="secondary"
                      />
                    }
                    label="Advanced Statistics"
                  />
                  {showAdvancedStats && (
                    <Typography variant="caption" color="text.secondary" display="block">
                      Includes: Geometric mean, Harmonic mean, Trimmed mean, MAD
                    </Typography>
                  )}
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={filterOutliers}
                        onChange={(e) => setFilterOutliers(e.target.checked)}
                        color="secondary"
                      />
                    }
                    label={
                      <Box display="flex" alignItems="center">
                        <FilterAltIcon sx={{ mr: 0.5, fontSize: 20 }} />
                        Filter Outliers
                      </Box>
                    }
                  />
                  {filterOutliers && (
                    <Typography variant="caption" color="text.secondary" display="block">
                      Removes outliers before analysis
                    </Typography>
                  )}
                </Grid>
                
                {includeConfidenceIntervals && (
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Confidence Level</InputLabel>
                      <Select
                        value={confidenceLevel}
                        onChange={(e) => setConfidenceLevel(e.target.value as number)}
                        label="Confidence Level"
                      >
                        <MenuItem value={0.90}>90%</MenuItem>
                        <MenuItem value={0.95}>95%</MenuItem>
                        <MenuItem value={0.99}>99%</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}
                
                {comparisonMode && (
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Grouping Variable</InputLabel>
                      <Select
                        value={groupingVariable}
                        onChange={(e) => setGroupingVariable(e.target.value)}
                        label="Grouping Variable"
                        disabled={categoricalColumns.length === 0}
                      >
                        {categoricalColumns.length === 0 ? (
                          <MenuItem value="" disabled>
                            No categorical variables available
                          </MenuItem>
                        ) : (
                          categoricalColumns.map(column => (
                            <MenuItem key={column.id} value={column.id}>
                              {column.name}
                            </MenuItem>
                          ))
                        )}
                      </Select>
                    </FormControl>
                  </Grid>
                )}
                
                {filterOutliers && (
                  <>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormControl component="fieldset">
                        <FormLabel component="legend" sx={{ fontSize: '0.875rem' }}>Outlier Method</FormLabel>
                        <RadioGroup
                          value={outlierMethod}
                          onChange={(e) => setOutlierMethod(e.target.value as 'iqr' | 'zscore')}
                          row
                        >
                          <FormControlLabel value="iqr" control={<Radio size="small" />} label="IQR" />
                          <FormControlLabel value="zscore" control={<Radio size="small" />} label="Z-Score" />
                        </RadioGroup>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={3}>
                      <TextField
                        label={outlierMethod === 'iqr' ? 'IQR Multiplier' : 'Z-Score Threshold'}
                        type="number"
                        size="small"
                        fullWidth
                        value={outlierThreshold}
                        onChange={(e) => setOutlierThreshold(parseFloat(e.target.value) || 1.5)}
                        inputProps={{
                          min: 0.1,
                          max: 5,
                          step: 0.1
                        }}
                        helperText={outlierMethod === 'iqr' ? 'Default: 1.5' : 'Default: 3'}
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
          
          <Box mt={3} display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              color="primary"
              size="large"
              startIcon={<CalculateIcon />}
              onClick={runDescriptiveAnalysis}
              disabled={loading || selectedColumns.length === 0 || (comparisonMode && !groupingVariable)}
              sx={{ minWidth: 200 }}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : 'Calculate Statistics'}
            </Button>
            
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<RefreshIcon />}
              onClick={() => {
                setDescriptiveResults({});
                localStorage.removeItem('descriptive_analysis_results');
              }}
              disabled={Object.keys(descriptiveResults).length === 0}
            >
              Clear Results
            </Button>
            
            {Object.keys(descriptiveResults).length > 0 && (
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={exportToCSV}
              >
                Export CSV
              </Button>
            )}
          </Box>
        </Paper>
      </Fade>
      
      {error && (
        <Zoom in>
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        </Zoom>
      )}
      
      {Object.keys(descriptiveResults).length > 0 && !loading && (
        <Fade in timeout={700}>
          <Box>
            {comparisonMode && selectedColumns.length > 1 && !groupingVariable && (
              <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <CompareArrowsIcon sx={{ mr: 1 }} />
                  Variable Comparison
                </Typography>
                
                <Grid container spacing={3}>
                  {generateComparisonData().length > 0 && (
                    <Grid item xs={12} md={6}>
                      <Box height={400}>
                        <ResponsiveContainer width="100%" height="100%">
                          <RadarChart data={generateComparisonData()}>
                            <PolarGrid />
                            <PolarAngleAxis dataKey="metric" />
                            <PolarRadiusAxis />
                            {selectedColumns.map((columnId, index) => {
                              const column = descriptiveResults[columnId]?.column;
                              if (!column) return null;
                              return (
                                <Radar
                                  key={columnId}
                                  name={column.name}
                                  dataKey={column.name}
                                  stroke={theme.palette[['primary', 'secondary', 'success', 'warning'][index % 4] as 'primary' | 'secondary' | 'success' | 'warning'].main}
                                  fill={theme.palette[['primary', 'secondary', 'success', 'warning'][index % 4] as 'primary' | 'secondary' | 'success' | 'warning'].main}
                                  fillOpacity={0.3}
                                />
                              );
                            })}
                            <Legend />
                          </RadarChart>
                        </ResponsiveContainer>
                      </Box>
                    </Grid>
                  )}
                  
                  <Grid item xs={12} md={6}>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Variable</TableCell>
                            <TableCell align="right">Mean</TableCell>
                            <TableCell align="right">Std Dev</TableCell>
                            <TableCell align="right">CV%</TableCell>
                            <TableCell align="right">Normal?</TableCell>
                            {filterOutliers && <TableCell align="right">Outliers</TableCell>}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {selectedColumns.map(columnId => {
                            const result = descriptiveResults[columnId];
                            if (!result) return null;
                            return (
                              <TableRow key={columnId}>
                                <TableCell>{result.column.name}</TableCell>
                                <TableCell align="right">{result.statistics.mean.toFixed(2)}</TableCell>
                                <TableCell align="right">{result.statistics.standardDeviation.toFixed(2)}</TableCell>
                                <TableCell align="right">{result.statistics.coefficientOfVariation?.toFixed(1)}%</TableCell>
                                <TableCell align="right">
                                  {result.statistics.isNormal ? (
                                    result.statistics.isNormal.isNormal ? (
                                      <CheckCircleIcon color="success" fontSize="small" />
                                    ) : (
                                      <WarningIcon color="warning" fontSize="small" />
                                    )
                                  ) : '-'}
                                </TableCell>
                                {filterOutliers && (
                                  <TableCell align="right">
                                    {result.statistics.outliersRemoved || 0}
                                  </TableCell>
                                )}
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>
                </Grid>
              </Paper>
            )}
            
            <Paper elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
              >
                <Tab label="Summary" icon={<AssessmentIcon />} iconPosition="start" />
                <Tab label="Detailed Statistics" icon={<FunctionsIcon />} iconPosition="start" />
                <Tab label="Visualizations" icon={<InsertChartIcon />} iconPosition="start" />
                {comparisonMode && groupingVariable && (
                  <Tab label="Group Comparison" icon={<GroupWorkIcon />} iconPosition="start" />
                )}
              </Tabs>
              
              <Box p={3}>
                {activeTab === 0 && (
                  <Grid container spacing={3}>
                    {Object.entries(descriptiveResults).map(([columnId, result]) => (
                      <Grid item xs={12} key={columnId}>
                        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                          <TimelineIcon sx={{ mr: 1 }} />
                          {result.column.name}
                          {filterOutliers && result.statistics.outliersRemoved !== undefined && (
                            <Chip 
                              label={`${result.statistics.outliersRemoved} outliers removed`}
                              size="small"
                              color="warning"
                              sx={{ ml: 2 }}
                            />
                          )}
                        </Typography>
                        
                        {viewMode === 'grid' ? (
                          <Grid container spacing={3}>
                            <Grid item xs={12} sm={6} md={3}>
                              <StatCard
                                title="Mean"
                                value={result.statistics.mean.toFixed(2)}
                                subtitle={includeConfidenceIntervals && result.statistics.confidenceInterval
                                  ? `CI: [${result.statistics.confidenceInterval[0].toFixed(2)}, ${result.statistics.confidenceInterval[1].toFixed(2)}]`
                                  : undefined}
                                icon={<SpeedIcon />}
                                color="primary"
                              />
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                              <StatCard
                                title="Standard Deviation"
                                value={result.statistics.standardDeviation.toFixed(2)}
                                subtitle={`CV: ${result.statistics.coefficientOfVariation?.toFixed(1)}%`}
                                icon={<TimelineIcon />}
                                color="secondary"
                              />
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                              <StatCard
                                title="Sample Size"
                                value={result.statistics.n}
                                subtitle={result.statistics.missing > 0 ? `${result.statistics.missing} missing` : 'No missing'}
                                icon={<FunctionsIcon />}
                                color="success"
                              />
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                              <StatCard
                                title="Distribution"
                                value={result.statistics.isNormal?.isNormal ? 'Normal' : 'Non-normal'}
                                subtitle={result.statistics.isNormal ? `p = ${result.statistics.isNormal.pValue.toFixed(3)}` : 'Not tested'}
                                icon={result.statistics.isNormal?.isNormal ? <CheckCircleIcon /> : <WarningIcon />}
                                color={result.statistics.isNormal?.isNormal ? 'success' : 'warning'}
                              />
                            </Grid>
                          </Grid>
                        ) : (
                          <List>
                            <ListItem>
                              <ListItemIcon>
                                <SpeedIcon color="primary" />
                              </ListItemIcon>
                              <ListItemText
                                primary="Mean"
                                secondary={
                                  <Box>
                                    <Typography variant="h6" component="span">
                                      {result.statistics.mean.toFixed(2)}
                                    </Typography>
                                    {includeConfidenceIntervals && result.statistics.confidenceInterval && (
                                      <Typography variant="body2" color="text.secondary">
                                        CI: [{result.statistics.confidenceInterval[0].toFixed(2)}, {result.statistics.confidenceInterval[1].toFixed(2)}]
                                      </Typography>
                                    )}
                                  </Box>
                                }
                              />
                            </ListItem>
                            <Divider />
                            <ListItem>
                              <ListItemIcon>
                                <TimelineIcon color="secondary" />
                              </ListItemIcon>
                              <ListItemText
                                primary="Standard Deviation"
                                secondary={
                                  <Box>
                                    <Typography variant="h6" component="span">
                                      {result.statistics.standardDeviation.toFixed(2)}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      CV: {result.statistics.coefficientOfVariation?.toFixed(1)}%
                                    </Typography>
                                  </Box>
                                }
                              />
                            </ListItem>
                            <Divider />
                            <ListItem>
                              <ListItemIcon>
                                <FunctionsIcon color="success" />
                              </ListItemIcon>
                              <ListItemText
                                primary="Sample Size"
                                secondary={
                                  <Box>
                                    <Typography variant="h6" component="span">
                                      {result.statistics.n}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      {result.statistics.missing > 0 ? `${result.statistics.missing} missing` : 'No missing'}
                                    </Typography>
                                  </Box>
                                }
                              />
                            </ListItem>
                            <Divider />
                            <ListItem>
                              <ListItemIcon>
                                {result.statistics.isNormal?.isNormal ? (
                                  <CheckCircleIcon color="success" />
                                ) : (
                                  <WarningIcon color="warning" />
                                )}
                              </ListItemIcon>
                              <ListItemText
                                primary="Distribution"
                                secondary={
                                  <Box>
                                    <Typography variant="h6" component="span">
                                      {result.statistics.isNormal?.isNormal ? 'Normal' : 'Non-normal'}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      {result.statistics.isNormal ? `p = ${result.statistics.isNormal.pValue.toFixed(3)}` : 'Not tested'}
                                    </Typography>
                                  </Box>
                                }
                              />
                            </ListItem>
                          </List>
                        )}
                        
                        {result.boxPlotData.outliers.length > 0 && !filterOutliers && (
                          <Alert severity="warning" sx={{ mt: 2 }}>
                            <Typography variant="body2">
                              <strong>{result.boxPlotData.outliers.length} outliers detected</strong> 
                              {' (included in analysis)'}
                            </Typography>
                          </Alert>
                        )}
                        
                        <Divider sx={{ my: 3 }} />
                      </Grid>
                    ))}
                  </Grid>
                )}
                
                {activeTab === 1 && (
                  <Box>
                    {Object.entries(descriptiveResults).map(([columnId, result]) => (
                      <Paper key={columnId} elevation={2} sx={{ p: 3, mb: 3, bgcolor: 'background.paper' }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TimelineIcon color="primary" />
                            {result.column.name} - Detailed Statistics
                          </Typography>
                          {filterOutliers && result.statistics.outliersRemoved !== undefined && (
                            <Chip
                              label={`${result.statistics.outliersRemoved} outliers removed`}
                              size="small"
                              color="warning"
                              variant="outlined"
                            />
                          )}
                        </Box>

                        {/* Sample Information Overview */}
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <InfoIcon fontSize="small" />
                            Sample Information
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} md={3}>
                              <StatsCard
                                title="Sample Size"
                                value={result.statistics.n.toLocaleString()}
                                description="Valid observations"
                                color="primary"
                                variant="outlined"
                                icon={<PeopleIcon />}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                              <StatsCard
                                title="Missing Values"
                                value={result.statistics.missing.toLocaleString()}
                                description={`${((result.statistics.missing / (result.statistics.n + result.statistics.missing)) * 100).toFixed(1)}% missing`}
                                color={result.statistics.missing > 0 ? "warning" : "success"}
                                variant="outlined"
                                icon={<DataUsageIcon />}
                              />
                            </Grid>
                            {filterOutliers && result.statistics.originalN && (
                              <Grid item xs={12} sm={6} md={3}>
                                <StatsCard
                                  title="Original Sample"
                                  value={result.statistics.originalN.toLocaleString()}
                                  description="Before outlier removal"
                                  color="info"
                                  variant="outlined"
                                  icon={<FilterAltIcon />}
                                />
                              </Grid>
                            )}
                            {filterOutliers && result.statistics.outliersRemoved !== undefined && (
                              <Grid item xs={12} sm={6} md={3}>
                                <StatsCard
                                  title="Outliers Removed"
                                  value={result.statistics.outliersRemoved.toLocaleString()}
                                  description={`${((result.statistics.outliersRemoved / (result.statistics.originalN || result.statistics.n)) * 100).toFixed(1)}% of data`}
                                  color="warning"
                                  variant="outlined"
                                  icon={<ScatterPlotIcon />}
                                />
                              </Grid>
                            )}
                          </Grid>
                        </Box>

                        {/* Central Tendency */}
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <CenterFocusStrongIcon fontSize="small" />
                            Central Tendency
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} md={3}>
                              <StatsCard
                                title="Mean"
                                value={result.statistics.mean.toFixed(4)}
                                description={includeConfidenceIntervals && result.statistics.confidenceInterval
                                  ? `CI: [${result.statistics.confidenceInterval[0].toFixed(2)}, ${result.statistics.confidenceInterval[1].toFixed(2)}]`
                                  : "Arithmetic average"}
                                color="primary"
                                variant="outlined"
                                icon={<SpeedIcon />}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                              <StatsCard
                                title="Median"
                                value={result.statistics.median.toFixed(4)}
                                description="Middle value (50th percentile)"
                                color="secondary"
                                variant="outlined"
                                icon={<StraightenIcon />}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6} md={3}>
                              <StatsCard
                                title="Mode"
                                value={result.statistics.modeResult.mode !== null
                                  ? result.statistics.modeResult.mode.toFixed(4)
                                  : 'N/A'}
                                description={result.statistics.modeResult.message || "Most frequent value"}
                                color="info"
                                variant="outlined"
                                icon={<BarChartIcon />}
                              />
                            </Grid>
                            {showAdvancedStats && result.statistics.trimmedMean && (
                              <Grid item xs={12} sm={6} md={3}>
                                <StatsCard
                                  title="Trimmed Mean"
                                  value={result.statistics.trimmedMean.toFixed(4)}
                                  description="5% trimmed mean"
                                  color="secondary"
                                  variant="outlined"
                                  icon={<TrendingFlatIcon />}
                                />
                              </Grid>
                            )}
                          </Grid>
                        </Box>

                        {/* Advanced Central Tendency (if enabled) */}
                        {showAdvancedStats && (
                          <Box sx={{ mb: 3 }}>
                            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                              <FunctionsIcon fontSize="small" />
                              Advanced Central Tendency
                            </Typography>
                            <Grid container spacing={2}>
                              {result.statistics.geometricMean && (
                                <Grid item xs={12} sm={6} md={4}>
                                  <StatsCard
                                    title="Geometric Mean"
                                    value={result.statistics.geometricMean.toFixed(4)}
                                    description="For positive values only"
                                    color="info"
                                    variant="outlined"
                                    icon={<CalculateIcon />}
                                  />
                                </Grid>
                              )}
                              {result.statistics.harmonicMean && (
                                <Grid item xs={12} sm={6} md={4}>
                                  <StatsCard
                                    title="Harmonic Mean"
                                    value={result.statistics.harmonicMean.toFixed(4)}
                                    description="For positive values only"
                                    color="info"
                                    variant="outlined"
                                    icon={<CalculateIcon />}
                                  />
                                </Grid>
                              )}
                            </Grid>
                          </Box>
                        )}

                        {/* Detailed Tables Section */}
                        <Grid container spacing={3}>
                          <Grid item xs={12} lg={6}>
                            <Paper elevation={0} variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                                <TimelineIcon fontSize="small" />
                                Variability & Dispersion
                              </Typography>
                              <TableContainer>
                                <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Statistic</TableCell>
                                      <TableCell align="right">Value</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Minimum</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.min.toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Maximum</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.max.toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Range</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.range.toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Variance</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.variance.toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Standard Deviation</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.standardDeviation.toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Standard Error</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.standardError?.toFixed(4) || 'N/A'}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Coefficient of Variation</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.coefficientOfVariation?.toFixed(2)}%
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>IQR</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.iqr.toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    {showAdvancedStats && result.statistics.mad && (
                                      <TableRow>
                                        <TableCell sx={{ fontWeight: 500 }}>Median Absolute Deviation</TableCell>
                                        <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                          {result.statistics.mad.toFixed(4)}
                                        </TableCell>
                                      </TableRow>
                                    )}

                                  </TableBody>
                                </Table>
                              </TableContainer>
                            </Paper>
                          </Grid>

                          {/* Position/Percentiles and Distribution */}
                          <Grid item xs={12} lg={6}>
                            <Paper elevation={0} variant="outlined" sx={{ p: 2, height: 'fit-content' }}>
                              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                                <StraightenIcon fontSize="small" />
                                Position & Percentiles
                              </Typography>
                              <TableContainer>
                                <Table size="small" sx={{ '& .MuiTableCell-head': { bgcolor: 'grey.50', fontWeight: 600 } }}>
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Percentile</TableCell>
                                      <TableCell align="right">Value</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {showAdvancedStats && result.statistics.percentiles && (
                                      <>
                                        <TableRow>
                                          <TableCell sx={{ fontWeight: 500 }}>5th Percentile</TableCell>
                                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                            {result.statistics.percentiles.p5.toFixed(4)}
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell sx={{ fontWeight: 500 }}>10th Percentile</TableCell>
                                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                            {result.statistics.percentiles.p10.toFixed(4)}
                                          </TableCell>
                                        </TableRow>
                                      </>
                                    )}
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Q1 (25th Percentile)</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.quartiles[0].toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Q2 (50th Percentile)</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.quartiles[1].toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 500 }}>Q3 (75th Percentile)</TableCell>
                                      <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                        {result.statistics.quartiles[2].toFixed(4)}
                                      </TableCell>
                                    </TableRow>
                                    {showAdvancedStats && result.statistics.percentiles && (
                                      <>
                                        <TableRow>
                                          <TableCell sx={{ fontWeight: 500 }}>90th Percentile</TableCell>
                                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                            {result.statistics.percentiles.p90.toFixed(4)}
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell sx={{ fontWeight: 500 }}>95th Percentile</TableCell>
                                          <TableCell align="right" sx={{ fontFamily: 'monospace' }}>
                                            {result.statistics.percentiles.p95.toFixed(4)}
                                          </TableCell>
                                        </TableRow>
                                      </>
                                    )}
                                  </TableBody>
                                </Table>
                              </TableContainer>
                            </Paper>
                          </Grid>
                        </Grid>

                        {/* Distribution Shape and Normality */}
                        {(includeShape || includeNormalityTest || includeConfidenceIntervals) && (
                          <Box sx={{ mt: 3 }}>
                            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                              <ShowChartIcon fontSize="small" />
                              Distribution Properties
                            </Typography>
                            <Grid container spacing={2}>
                              {includeShape && result.statistics.skewness !== undefined && (
                                <Grid item xs={12} sm={6} md={3}>
                                  <StatsCard
                                    title="Skewness"
                                    value={result.statistics.skewness.toFixed(4)}
                                    description={
                                      Math.abs(result.statistics.skewness) < 0.5 ? 'Symmetric' :
                                      result.statistics.skewness > 0 ? 'Right-skewed' : 'Left-skewed'
                                    }
                                    color={Math.abs(result.statistics.skewness) < 0.5 ? 'success' : 'warning'}
                                    variant="outlined"
                                    icon={<TrendingUpIcon />}
                                  />
                                </Grid>
                              )}
                              {includeShape && result.statistics.kurtosis !== undefined && (
                                <Grid item xs={12} sm={6} md={3}>
                                  <StatsCard
                                    title="Kurtosis"
                                    value={result.statistics.kurtosis.toFixed(4)}
                                    description={
                                      Math.abs(result.statistics.kurtosis - 3) < 0.5 ? 'Mesokurtic' :
                                      result.statistics.kurtosis > 3 ? 'Leptokurtic' : 'Platykurtic'
                                    }
                                    color={Math.abs(result.statistics.kurtosis - 3) < 0.5 ? 'success' : 'info'}
                                    variant="outlined"
                                    icon={<BarChartIcon />}
                                  />
                                </Grid>
                              )}
                              {includeNormalityTest && result.statistics.isNormal && (
                                <Grid item xs={12} sm={6} md={3}>
                                  <StatsCard
                                    title="Normality Test"
                                    value={result.statistics.isNormal.isNormal ? 'Normal' : 'Non-normal'}
                                    description={`p = ${result.statistics.isNormal.pValue.toFixed(4)}`}
                                    color={result.statistics.isNormal.isNormal ? 'success' : 'warning'}
                                    variant="outlined"
                                    icon={result.statistics.isNormal.isNormal ? <CheckCircleIcon /> : <WarningIcon />}
                                  />
                                </Grid>
                              )}
                              {includeConfidenceIntervals && result.statistics.confidenceInterval && (
                                <Grid item xs={12} sm={6} md={3}>
                                  <StatsCard
                                    title={`${(confidenceLevel * 100).toFixed(0)}% CI for Mean`}
                                    value={`[${result.statistics.confidenceInterval[0].toFixed(2)}, ${result.statistics.confidenceInterval[1].toFixed(2)}]`}
                                    description="Confidence interval"
                                    color="info"
                                    variant="outlined"
                                    icon={<AssessmentIcon />}
                                  />
                                </Grid>
                              )}
                            </Grid>
                          </Box>
                        )}
                      </Paper>
                    ))}
                  </Box>
                )}
                
                {activeTab === 2 && (
                  <Box>
                    <Box mb={3}>
                      <ToggleButtonGroup
                        value={visualizationType}
                        exclusive
                        onChange={(_, newType) => newType && setVisualizationType(newType)}
                        size="small"
                      >
                        <ToggleButton value="histogram">
                          <MuiTooltip title="Histogram">
                            <span>
                              <BarChartIcon />
                            </span>
                          </MuiTooltip>
                        </ToggleButton>
                        <ToggleButton value="density">
                          <MuiTooltip title="Density Plot">
                            <span>
                              <ShowChartIcon />
                            </span>
                          </MuiTooltip>
                        </ToggleButton>
                        <ToggleButton value="cumulative">
                          <MuiTooltip title="Cumulative Distribution">
                            <span>
                              <TimelineIcon />
                            </span>
                          </MuiTooltip>
                        </ToggleButton>
                        <ToggleButton value="qqplot">
                          <MuiTooltip title="Q-Q Plot">
                            <span>
                              <BubbleChartIcon />
                            </span>
                          </MuiTooltip>
                        </ToggleButton>
                        <ToggleButton value="boxplot">
                          <MuiTooltip title="Box Plot">
                            <span>
                              <InsertChartIcon />
                            </span>
                          </MuiTooltip>
                        </ToggleButton>
                      </ToggleButtonGroup>
                    </Box>
                    
                    {Object.entries(descriptiveResults).map(([columnId, result]) => (
                      <Paper key={columnId} elevation={1} sx={{ p: 2, mb: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          {result.column.name}
                        </Typography>
                        
                        {visualizationType === 'histogram' && result.histogramData && result.histogramData.length > 0 && (
                          <Box height={400}>
                            <ResponsiveContainer width="100%" height="100%" key={`histogram-${columnId}`}>
                              <ComposedChart
                                data={result.histogramData}
                                margin={{
                                  top: 10,
                                  right: 30,
                                  left: 20,
                                  bottom: 70,
                                }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="binMiddle"
                                  tickFormatter={(value) => value.toFixed(2)}
                                  label={{ value: result.column.name, position: 'insideBottom', offset: -30 }}
                                />
                                <YAxis
                                  yAxisId="left"
                                  label={{ value: 'Frequency', angle: -90, position: 'insideLeft' }}
                                />
                                <YAxis
                                  yAxisId="right"
                                  orientation="right"
                                  tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                                  label={{ value: 'Cumulative %', angle: 90, position: 'insideRight' }}
                                />
                                <RechartsTooltip
                                  formatter={(value: any, name: string) => [
                                    name === 'frequency' ? value : `${(value * 100).toFixed(1)}%`,
                                    name === 'frequency' ? 'Frequency' : 'Cumulative %'
                                  ]}
                                  labelFormatter={(value: number) => {
                                    const bin = result.histogramData.find(item => item.binMiddle === value);
                                    return bin ? `Range: ${bin.bin}` : '';
                                  }}
                                />
                                <Legend verticalAlign="top" align="center" />
                                <Bar
                                  yAxisId="left"
                                  dataKey="frequency"
                                  fill={theme.palette.primary.main}
                                  name="Frequency"
                                  barSize={Math.max(20, Math.min(60, 800 / result.histogramData.length))}
                                  opacity={0.8}
                                >
                                  {result.histogramData.map((entry, index) => (
                                    <Cell
                                      key={`cell-${index}`}
                                      fill={entry.binMiddle < result.statistics.mean ? theme.palette.primary.light : theme.palette.primary.main}
                                    />
                                  ))}
                                </Bar>
                                <Line
                                  yAxisId="right"
                                  type="monotone"
                                  dataKey="cumulativeFrequency"
                                  stroke={theme.palette.secondary.main}
                                  strokeWidth={2}
                                  name="Cumulative %"
                                  dot={false}
                                />
                                <ReferenceLine
                                  x={result.statistics.mean}
                                  stroke="red"
                                  strokeDasharray="3 3"
                                  yAxisId="left">
                                  <Label value="Mean" position="top" fill="red" />
                                </ReferenceLine>
                                <ReferenceLine
                                  x={result.statistics.median}
                                  stroke="green"
                                  strokeDasharray="3 3"
                                  yAxisId="left">
                                  <Label value="Median" position="top" fill="green" />
                                </ReferenceLine>
                              </ComposedChart>
                            </ResponsiveContainer>
                          </Box>
                        )}
                        
                        {visualizationType === 'density' && result.densityData && result.densityData.length > 0 && (
                          <Box height={400}>
                            <ResponsiveContainer width="100%" height="100%" key={`density-${columnId}`}>
                              <AreaChart
                                data={result.densityData}
                                margin={{
                                  top: 20,
                                  right: 30,
                                  left: 20,
                                  bottom: 60,
                                }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="x"
                                  type="number"
                                  domain={['dataMin', 'dataMax']}
                                  tickFormatter={(value) => value.toFixed(2)}
                                  label={{ value: result.column.name, position: 'insideBottom', offset: -10 }}
                                />
                                <YAxis
                                  yAxisId="left"
                                  label={{ value: 'Density', angle: -90, position: 'insideLeft' }}
                                />
                                <RechartsTooltip
                                  formatter={(value: number) => value.toFixed(4)}
                                  labelFormatter={(value) => `x: ${value.toFixed(2)}`}
                                />
                                <Area
                                  type="monotone"
                                  dataKey="y"
                                  yAxisId="left"
                                  stroke={theme.palette.primary.main}
                                  fill={theme.palette.primary.light}
                                  fillOpacity={0.6}
                                  isAnimationActive={true}
                                  name="Density"
                                />
                                <ReferenceLine
                                  x={result.statistics.mean}
                                  stroke="red"
                                  strokeDasharray="3 3"
                                  yAxisId="left">
                                  <Label value="Mean" position="top" fill="red" />
                                </ReferenceLine>
                              </AreaChart>
                            </ResponsiveContainer>
                          </Box>
                        )}
                        
                        {visualizationType === 'cumulative' && result.histogramData && result.histogramData.length > 0 && (
                          <Box height={400}>
                            <ResponsiveContainer width="100%" height="100%" key={`cumulative-${columnId}`}>
                              <LineChart
                                data={result.histogramData}
                                margin={{
                                  top: 20,
                                  right: 30,
                                  left: 20,
                                  bottom: 60,
                                }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="binMiddle"
                                  tickFormatter={(value) => value.toFixed(2)}
                                  label={{ value: result.column.name, position: 'insideBottom', offset: -10 }}                              />
                                <YAxis
                                  yAxisId="left"
                                  tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                                  label={{ value: 'Cumulative %', angle: -90, position: 'insideLeft' }}
                                />
                                <RechartsTooltip 
                                  formatter={(value: any) => `${(value * 100).toFixed(1)}%`}
                                  labelFormatter={(value) => `Value: ${value.toFixed(2)}`}
                                />
                                <Line
                                  type="stepAfter"
                                  dataKey="cumulativeFrequency"
                                  yAxisId="left"
                                  stroke={theme.palette.primary.main}
                                  strokeWidth={2}
                                  dot={true}
                                />
                                <ReferenceLine
                                  x={result.statistics.mean}
                                  stroke="red"
                                  strokeDasharray="3 3"
                                  yAxisId="left">
                                  <Label value="Mean" position="top" fill="red" />
                                </ReferenceLine>
                                <ReferenceLine
                                  x={result.statistics.median}
                                  stroke="green"
                                  strokeDasharray="3 3"
                                  yAxisId="left">
                                  <Label value="Median" position="insideTopRight" fill="green" />
                                </ReferenceLine>
                                <ReferenceLine
                                  y={0.5}
                                  stroke="blue"
                                  strokeDasharray="3 3"
                                  yAxisId="left">
                                  <Label value="50%" position="right" fill="blue" />
                                </ReferenceLine>
                              </LineChart>
                            </ResponsiveContainer>
                          </Box>
                        )}
                        
                        {visualizationType === 'qqplot' && result.qqPlotData && result.qqPlotData.length > 0 && (
                          <Box height={400}>
                            <ResponsiveContainer width="100%" height="100%">
                              <ScatterChart
                                margin={{
                                  top: 20,
                                  right: 30,
                                  left: 20,
                                  bottom: 60,
                                }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis 
                                  type="number"
                                  dataKey="theoretical"
                                  name="Theoretical Quantiles"
                                  label={{ value: 'Theoretical Quantiles', position: 'insideBottom', offset: -10 }}
                                />
                                <YAxis
                                  yAxisId="left"
                                  type="number"
                                  dataKey="sample"
                                  name="Sample Quantiles"
                                  label={{ value: 'Sample Quantiles', angle: -90, position: 'insideLeft' }}
                                />
                                <RechartsTooltip cursor={{ strokeDasharray: '3 3' }} />
                                <Scatter
                                  name="Q-Q Plot"
                                  data={result.qqPlotData}
                                  yAxisId="left"
                                  fill={theme.palette.primary.main}
                                />
                                {result.qqPlotData.length > 1 && (
                                  <Line
                                    yAxisId="left"
                                    type="monotone"
                                    data={[
                                      {
                                        theoretical: Math.min(...result.qqPlotData.map(d => d.theoretical)),
                                        sample: Math.min(...result.qqPlotData.map(d => d.sample))
                                      },
                                      {
                                        theoretical: Math.max(...result.qqPlotData.map(d => d.theoretical)),
                                        sample: Math.max(...result.qqPlotData.map(d => d.sample))
                                      }
                                    ]}
                                    dataKey="sample"
                                    stroke="red"
                                    strokeDasharray="3 3"
                                    dot={false}
                                    activeDot={false}
                                    name="Reference Line"
                                  />
                                )}
                              </ScatterChart>
                            </ResponsiveContainer>
                          </Box>
                        )}
                        
                        {visualizationType === 'boxplot' && includeBoxPlot && (
                          <Box height={200}>
                            <Suspense fallback={
                              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                                <CircularProgress size={20} />
                              </Box>
                            }>
                              <PlotlyBoxPlot
                                columnId={columnId}
                                boxPlotData={result.boxPlotData}
                                columnName={result.column.name}
                                includeOutliers={includeOutliers}
                              />
                            </Suspense>
                          </Box>
                        )}
                      </Paper>
                    ))}
                  </Box>
                )}
                
                {activeTab === 3 && comparisonMode && groupingVariable && (
                  <Box>
                    {Object.entries(descriptiveResults).map(([columnId, result]) => (
                      <Paper key={columnId} elevation={1} sx={{ p: 2, mb: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          {result.column.name} by {categoricalColumns.find(col => col.id === groupingVariable)?.name}
                        </Typography>
                        
                        {result.groupStatistics && (
                          <>
                            <Box height={400} mb={3}>
                              <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                  data={generateGroupComparisonData(columnId)}
                                  margin={{
                                    top: 20,
                                    right: 30,
                                    left: 20,
                                    bottom: 60,
                                  }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" />
                                  <XAxis 
                                    dataKey="group" 
                                    angle={-45}
                                    textAnchor="end"
                                    height={80}
                                  />
                                  <YAxis />
                                  <RechartsTooltip />
                                  <Legend />
                                  <Bar dataKey="mean" fill={theme.palette.primary.main} name="Mean" />
                                  <Bar dataKey="median" fill={theme.palette.secondary.main} name="Median" />
                                </BarChart>
                              </ResponsiveContainer>
                            </Box>
                            
                            <TableContainer>
                              <Table size="small">
                                <TableHead>
                                  <TableRow>
                                    <TableCell>Group</TableCell>
                                    <TableCell align="right">N</TableCell>
                                    <TableCell align="right">Mean</TableCell>
                                    <TableCell align="right">Median</TableCell>
                                    <TableCell align="right">Std Dev</TableCell>
                                    <TableCell align="right">Min</TableCell>
                                    <TableCell align="right">Max</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {Object.entries(result.groupStatistics).map(([group, stats]: [string, any]) => (
                                    <TableRow key={group}>
                                      <TableCell>{group}</TableCell>
                                      <TableCell align="right">{stats.n}</TableCell>
                                      <TableCell align="right">{stats.mean.toFixed(2)}</TableCell>
                                      <TableCell align="right">{stats.median.toFixed(2)}</TableCell>
                                      <TableCell align="right">{stats.std.toFixed(2)}</TableCell>
                                      <TableCell align="right">{stats.min.toFixed(2)}</TableCell>
                                      <TableCell align="right">{stats.max.toFixed(2)}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </>
                        )}
                      </Paper>
                    ))}
                  </Box>
                )}
              </Box>
            </Paper>
          </Box>
        </Fade>
      )}
      
      {/* Loading skeleton */}
      {loading && (
        <Box>
          <Skeleton variant="rectangular" height={400} sx={{ mb: 2 }} />
          <Grid container spacing={2}>
            <Grid item xs={12} md={3}>
              <Skeleton variant="rectangular" height={120} />
            </Grid>
            <Grid item xs={12} md={3}>
              <Skeleton variant="rectangular" height={120} />
            </Grid>
            <Grid item xs={12} md={3}>
              <Skeleton variant="rectangular" height={120} />
            </Grid>
            <Grid item xs={12} md={3}>
              <Skeleton variant="rectangular" height={120} />
            </Grid>
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default DescriptiveAnalysis;